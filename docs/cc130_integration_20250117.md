# CC130 水表整合修改記錄

**時間戳記：** 2025-01-17  
**修改者：** Augment Agent  
**目的：** 整合 CC130 數位水表到系統中

## 修改概述

本次修改新增了 CC130 數位水表的支援，實作了基於 Modbus RTU 協議的數據讀取功能。CC130 水表支援讀取累計用量值和小數點位數，並能正確計算最終的水表讀數。

## 修改檔案清單

### 1. 新增檔案

#### 24dio/src/waterMeterNodeCc130.hpp
- **功能：** CC130 水表節點類別標頭檔
- **內容：** 
  - 定義 waterMeterNodeCc130 類別
  - 繼承自 analogNode
  - 包含累計用量值和小數點位數的成員變數
  - 提供 Modbus 數據處理方法

#### 24dio/src/waterMeterNodeCc130.cpp
- **功能：** CC130 水表節點類別實作檔
- **內容：**
  - 實作 CC130 水表的 Modbus RTU over TCP 通訊協議
  - 支援讀取寄存器 0（累計用量值）和寄存器 10（小數點位數）
  - 包含完整的 CRC 校驗檢查
  - 根據 CC130 規格計算最終水表讀數
  - 定期更新系統訊息
- **重要修正：**
  1. 修正為 Modbus RTU over TCP 協議，加入 CRC 校驗和正確的數據解析
- **寄存器地址修正：** 小數點位數寄存器地址從 10 修正為 215
- **CRC 校驗修正：** 修正 CRC 校驗邏輯，使用與其他裝置一致的比較方式
- **LOG 訊息改善：** 改善除錯訊息，可清楚分辨是小數點位數還是累計用量值的處理狀態
  2. 修正小數點位數寄存器地址從 10 改為 215
  3. 確認請求長度：累計用量值 2 個寄存器，小數點位數 1 個寄存器

### 2. 修改檔案

#### 24dio/src/wdef.h
- **修改位置：** 第 300 行
- **修改內容：** 在裝置類型枚舉中新增 `CC130_WATER_METER_DEVICE //53`
- **目的：** 定義 CC130 水表的裝置類型常數

#### 24dio/src/modbusNode.cpp
- **修改位置 1：** 第 49 行
- **修改內容：** 新增 `#include "waterMeterNodeCc130.hpp"`
- **目的：** 引入 CC130 水表節點類別

- **修改位置 2：** 第 231-237 行
- **修改內容：** 新增 CC130_WATER_METER_DEVICE 的處理邏輯
- **目的：** 註冊 CC130 水表類型，使系統能夠建立對應的節點實例

#### 24dio/CMakeLists.txt
- **修改位置：** 第 70 行
- **修改內容：** 新增 `add_library(MyLibrary ./src/waterMeterNodeCc130.cpp)`
- **目的：** 將 CC130 水表實作檔加入編譯設定

#### 24dio/src/modbusDev.cpp
- **修改位置：** 第 142 行
- **修改內容：** 在 `is_rs485_device()` 方法中加入 `dio_type == CC130_WATER_METER_DEVICE`
- **目的：** 確保系統正確識別 CC130 為 RS485 裝置，使用 Modbus RTU over TCP 協議，不會自動添加 TCP 標頭
- **重要性：** 這是解決 TCP 標頭問題的關鍵修正

#### web/com_water_meter_history-1.0.0/site/helpers/water_meter_history.php
- **修改位置：** 第 51 行
- **修改內容：** 在 `get_all_water_meter_devices()` 方法中，將查詢條件從 `device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32` 修改為 `device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32 or device.dio_type = 53`
- **目的：** 讓水表歷史記錄系統能夠識別和處理 CC130 水表裝置

#### web/com_floor-1.0.0/site/helpers/floor.php
- **修改位置 1：** 第 3679 行
- **修改內容：** 在 `getWaterMeterDevices()` 方法中，將查詢條件從 `(device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32)` 修改為 `(device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32 or device.dio_type = 53)`
- **目的：** 讓樓層管理系統能夠識別和顯示 CC130 水表裝置

- **修改位置 2：** 第 3650 行
- **修改內容：** 在 `getWaterMeterDioTypes()` 方法中，將返回陣列從 `array(6,20,32)` 修改為 `array(6,20,32,53)`
- **目的：** 讓系統能夠正確識別所有水表裝置類型，包括 CC130 水表

#### web/com_top-1.0.0/site/helpers/toputility.php
- **修改位置 1：** 第 89 行
- **修改內容：** 新增常數定義 `public static $rs485_cc130_water_meter_device = 53;`
- **目的：** 定義 CC130 水表的裝置類型常數

- **修改位置 2：** 第 2282 行
- **修改內容：** 在 `get_main_water_meters()` 方法中，將查詢條件陣列從 `array(self::$rs485_water_meter_device,self::$rs485_tkd_water_meter_device,self::$rs485_general_opc_water_meter_device)` 修改為包含 `self::$rs485_cc130_water_meter_device`
- **目的：** 讓主要水表查詢功能能夠包含 CC130 水表

#### web/com_top-1.0.0/site/helpers/top.php
- **修改位置：** 第 230 行
- **修改內容：** 在裝置類型描述陣列中新增 `53=>'CC130數位水表'`
- **目的：** 為 CC130 水表提供中文顯示名稱

#### web/com_myaccount-1.0.0/site/helpers/myaccount.php
- **修改位置 1：** 第 652 行
- **修改內容：** 新增常數定義 `MyaccountHelpersMyaccount::$water_meter_cc130 = 53;`
- **目的：** 定義 CC130 水表的裝置類型常數

- **修改位置 2：** 第 208 行
- **修改內容：** 在 `getWaterMeters()` 方法中，將查詢條件陣列從 `array(self::$water_meter, self::$water_meter_tkd, self::$water_meter_general)` 修改為包含 `self::$water_meter_cc130`
- **目的：** 讓個人帳戶系統能夠查詢和顯示 CC130 水表

## 技術規格實作

### CC130 水表通訊協議

#### 協議類型：Modbus RTU over TCP
- **特點：** 使用 TCP 傳輸但保持 RTU 格式，包含 CRC 校驗
- **無 TCP 標頭：** 直接傳送 RTU 格式數據，不包含 Modbus TCP 的 6 位元組標頭
- **CRC 校驗：** 每個訊息都包含 2 位元組的 CRC-16 校驗碼

#### 1. 累計用量值讀取
- **寄存器地址：** 0 (40001)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 4 bytes (2個寄存器)
- **計算公式：** X2 + (X1×256) + (X4×256²) + (X3×256³)
- **回應格式：** [地址][功能碼][數據長度][數據4位元組][CRC低][CRC高]

#### 2. 小數點位數讀取
- **寄存器地址：** 215 (40216)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 2 bytes (1個寄存器)
- **使用數據：** 僅使用低位元組 (Y2)
- **回應格式：** [地址][功能碼][數據長度][數據2位元組][CRC低][CRC高]

### 數據處理流程

1. **初始化階段：** 建立兩個 Modbus RTU 讀取命令（小數點位數和累計用量值），包含 CRC 校驗碼
2. **數據接收：** 接收 Modbus RTU over TCP 回應數據
3. **CRC 校驗：** 驗證接收數據的 CRC 校驗碼，確保數據完整性
4. **數據解析：** 根據 RTU 格式解析數據（無 TCP 標頭）
5. **數據計算：** 根據小數點位數計算最終水表讀數
6. **系統更新：** 定期將讀數更新到資料庫

## 程式碼特色

### 1. 註解完整
- 所有函數都有詳細的中文註解
- 重要計算邏輯都有說明
- 符合用戶要求的註解規範

### 2. 協議正確性
- **Modbus RTU over TCP**：正確實作 RTU 格式，無 TCP 標頭
- **CRC 校驗**：完整的 CRC-16 校驗檢查，確保數據完整性
- **位元組順序**：正確處理 Modbus RTU 的 CRC 位元組順序（低位元組在前）

### 3. 錯誤處理
- 檢查裝置地址匹配
- 驗證數據長度
- CRC 校驗錯誤檢測
- 驗證小數點位數有效性
- 提供詳細的除錯訊息

### 4. 效能優化
- 使用循環讀取機制
- 適當的更新頻率控制
- 記憶體使用優化

## 系統整合

### 裝置類型註冊
- 在 wdef.h 中定義 CC130_WATER_METER_DEVICE = 53
- 在 modbusNode.cpp 中註冊對應的處理邏輯
- 系統可自動識別並建立 CC130 水表節點

### 建置系統整合
- 更新 CMakeLists.txt 包含新的源文件
- 確保編譯系統能正確處理新增的檔案

### PHP 系統整合
- 更新 `com_water_meter_history` 組件的 helper 檔案，讓水表歷史記錄系統能夠識別 CC130 水表
- 更新 `com_floor` 組件的 helper 檔案，讓樓層管理系統能夠顯示 CC130 水表
- 確保所有水表相關的 PHP 查詢都包含 CC130 裝置類型 (dio_type = 53)

## 使用方式

### 設定檔配置
在系統設定中指定裝置類型為 CC130_WATER_METER_DEVICE (53)，系統將自動：
1. 建立 waterMeterNodeCc130 實例
2. 初始化 Modbus 通訊
3. 開始定期讀取水表數據
4. 更新系統資料庫

### 監控輸出
系統會輸出以下除錯訊息：
- `CC130 decimal_places: X` - 小數點位數讀取結果
- `CC130 water flow: X.XXX (raw: XXXX, decimal: X)` - 水表讀數計算結果
- `CC130 water meter update` - 系統更新訊息

## 注意事項

1. **數據依賴性：** 必須先成功讀取小數點位數，才能正確計算累計用量值
2. **通訊參數：** 確保 Modbus 通訊參數（波特率、停止位、校驗位）正確設定
3. **更新頻率：** 系統每分鐘更新一次水表讀數到資料庫
4. **數值範圍：** 支援 32-bit 無符號整數範圍的累計用量值
5. **PHP 系統相容性：** 所有水表相關的 PHP 查詢都已更新以支援 CC130 裝置類型
6. **資料庫整合：** CC130 水表的數據會自動儲存到 `#__water_meter_history` 表中

## 測試建議

1. **單元測試：** 測試 Modbus 命令建立和數據解析功能
2. **整合測試：** 驗證與實際 CC130 水表的通訊
3. **壓力測試：** 確認長時間運行的穩定性
4. **錯誤測試：** 驗證各種錯誤情況的處理

---

## 完成總結

### ✅ 已完成的修改

1. **C++ 核心實作**
   - 新增 `waterMeterNodeCc130.hpp` 和 `waterMeterNodeCc130.cpp`
   - 更新 `wdef.h` 定義裝置類型
   - 更新 `modbusNode.cpp` 註冊新裝置類型
   - 更新 `CMakeLists.txt` 建置設定

2. **PHP 系統整合**
   - 更新 `com_water_meter_history` 的 helper 檔案
   - 更新 `com_floor` 的 helper 檔案（2個方法）
   - 更新 `com_top` 的 helper 檔案（2個檔案，3個修改點）
   - 新增裝置類型中文描述

3. **文件記錄**
   - 建立詳細的修改記錄文件

### 📊 修改統計

- **新增檔案：** 2 個（.hpp + .cpp）
- **修改檔案：** 9 個
- **修改點數：** 16 個
- **涵蓋系統：** C++ 核心 + PHP Web 系統 + 建置系統

### 🔍 深入檢查結果

經過深入查找 Git commit 歷史和全面搜尋系統代碼，確認已涵蓋所有需要修改的檔案：

1. **C++ 核心系統**：完整實作並註冊 CC130 水表節點
2. **PHP Web 系統**：更新所有水表相關的查詢和常數定義
3. **管理後台**：`com_device` 的 dio_type 欄位支援手動輸入 53
4. **API 端點**：所有水表相關的 API 都會自動支援 CC130 水表
5. **歷史記錄**：水表歷史記錄系統完整支援 CC130 水表

### 📝 最終規格確認

- **小數點位數**：寄存器 215，請求長度 1 個寄存器
- **累計用量值**：寄存器 0，請求長度 2 個寄存器
- **通訊協議**：Modbus RTU over TCP，包含 CRC 校驗
- **數據計算**：根據 CC130 規格的特殊計算公式

**完成狀態：** ✅ 所有修改已完成
**下一步：** 建議進行編譯測試和實際硬體測試
