# CC130 水表整合修改記錄

**時間戳記：** 2025-01-17  
**修改者：** Augment Agent  
**目的：** 整合 CC130 數位水表到系統中

## 修改概述

本次修改新增了 CC130 數位水表的支援，實作了基於 Modbus RTU 協議的數據讀取功能。CC130 水表支援讀取累計用量值和小數點位數，並能正確計算最終的水表讀數。

## 修改檔案清單

### 1. 新增檔案

#### 24dio/src/waterMeterNodeCc130.hpp
- **功能：** CC130 水表節點類別標頭檔
- **內容：** 
  - 定義 waterMeterNodeCc130 類別
  - 繼承自 analogNode
  - 包含累計用量值和小數點位數的成員變數
  - 提供 Modbus 數據處理方法

#### 24dio/src/waterMeterNodeCc130.cpp
- **功能：** CC130 水表節點類別實作檔
- **內容：**
  - 實作 CC130 水表的 Modbus 通訊協議
  - 支援讀取寄存器 0（累計用量值）和寄存器 10（小數點位數）
  - 根據 CC130 規格計算最終水表讀數
  - 定期更新系統訊息

### 2. 修改檔案

#### 24dio/src/wdef.h
- **修改位置：** 第 300 行
- **修改內容：** 在裝置類型枚舉中新增 `CC130_WATER_METER_DEVICE //53`
- **目的：** 定義 CC130 水表的裝置類型常數

#### 24dio/src/modbusNode.cpp
- **修改位置 1：** 第 49 行
- **修改內容：** 新增 `#include "waterMeterNodeCc130.hpp"`
- **目的：** 引入 CC130 水表節點類別

- **修改位置 2：** 第 231-237 行
- **修改內容：** 新增 CC130_WATER_METER_DEVICE 的處理邏輯
- **目的：** 註冊 CC130 水表類型，使系統能夠建立對應的節點實例

#### 24dio/CMakeLists.txt
- **修改位置：** 第 70 行
- **修改內容：** 新增 `add_library(MyLibrary ./src/waterMeterNodeCc130.cpp)`
- **目的：** 將 CC130 水表實作檔加入編譯設定

#### web/com_water_meter_history-1.0.0/site/helpers/water_meter_history.php
- **修改位置：** 第 51 行
- **修改內容：** 在 `get_all_water_meter_devices()` 方法中，將查詢條件從 `device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32` 修改為 `device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32 or device.dio_type = 53`
- **目的：** 讓水表歷史記錄系統能夠識別和處理 CC130 水表裝置

#### web/com_floor-1.0.0/site/helpers/floor.php
- **修改位置 1：** 第 3679 行
- **修改內容：** 在 `getWaterMeterDevices()` 方法中，將查詢條件從 `(device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32)` 修改為 `(device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32 or device.dio_type = 53)`
- **目的：** 讓樓層管理系統能夠識別和顯示 CC130 水表裝置

- **修改位置 2：** 第 3650 行
- **修改內容：** 在 `getWaterMeterDioTypes()` 方法中，將返回陣列從 `array(6,20,32)` 修改為 `array(6,20,32,53)`
- **目的：** 讓系統能夠正確識別所有水表裝置類型，包括 CC130 水表

#### web/com_top-1.0.0/site/helpers/toputility.php
- **修改位置 1：** 第 89 行
- **修改內容：** 新增常數定義 `public static $rs485_cc130_water_meter_device = 53;`
- **目的：** 定義 CC130 水表的裝置類型常數

- **修改位置 2：** 第 2282 行
- **修改內容：** 在 `get_main_water_meters()` 方法中，將查詢條件陣列從 `array(self::$rs485_water_meter_device,self::$rs485_tkd_water_meter_device,self::$rs485_general_opc_water_meter_device)` 修改為包含 `self::$rs485_cc130_water_meter_device`
- **目的：** 讓主要水表查詢功能能夠包含 CC130 水表

#### web/com_top-1.0.0/site/helpers/top.php
- **修改位置：** 第 230 行
- **修改內容：** 在裝置類型描述陣列中新增 `53=>'CC130數位水表'`
- **目的：** 為 CC130 水表提供中文顯示名稱

#### web/com_myaccount-1.0.0/site/helpers/myaccount.php
- **修改位置 1：** 第 652 行
- **修改內容：** 新增常數定義 `MyaccountHelpersMyaccount::$water_meter_cc130 = 53;`
- **目的：** 定義 CC130 水表的裝置類型常數

- **修改位置 2：** 第 208 行
- **修改內容：** 在 `getWaterMeters()` 方法中，將查詢條件陣列從 `array(self::$water_meter, self::$water_meter_tkd, self::$water_meter_general)` 修改為包含 `self::$water_meter_cc130`
- **目的：** 讓個人帳戶系統能夠查詢和顯示 CC130 水表

## 技術規格實作

### CC130 水表通訊協議

#### 1. 累計用量值讀取
- **寄存器地址：** 0 (40001)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 4 bytes (2個寄存器)
- **計算公式：** X2 + (X1×256) + (X4×256²) + (X3×256³)

#### 2. 小數點位數讀取
- **寄存器地址：** 10 (40011)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 2 bytes (1個寄存器)
- **使用數據：** 僅使用低位元組 (Y2)

### 數據處理流程

1. **初始化階段：** 建立兩個 Modbus 讀取命令（小數點位數和累計用量值）
2. **數據讀取：** 循環讀取小數點位數和累計用量值
3. **數據計算：** 根據小數點位數計算最終水表讀數
4. **系統更新：** 定期將讀數更新到資料庫

## 程式碼特色

### 1. 註解完整
- 所有函數都有詳細的中文註解
- 重要計算邏輯都有說明
- 符合用戶要求的註解規範

### 2. 錯誤處理
- 檢查裝置地址匹配
- 驗證小數點位數有效性
- 提供詳細的除錯訊息

### 3. 效能優化
- 使用循環讀取機制
- 適當的更新頻率控制
- 記憶體使用優化

## 系統整合

### 裝置類型註冊
- 在 wdef.h 中定義 CC130_WATER_METER_DEVICE = 53
- 在 modbusNode.cpp 中註冊對應的處理邏輯
- 系統可自動識別並建立 CC130 水表節點

### 建置系統整合
- 更新 CMakeLists.txt 包含新的源文件
- 確保編譯系統能正確處理新增的檔案

### PHP 系統整合
- 更新 `com_water_meter_history` 組件的 helper 檔案，讓水表歷史記錄系統能夠識別 CC130 水表
- 更新 `com_floor` 組件的 helper 檔案，讓樓層管理系統能夠顯示 CC130 水表
- 確保所有水表相關的 PHP 查詢都包含 CC130 裝置類型 (dio_type = 53)

## 使用方式

### 設定檔配置
在系統設定中指定裝置類型為 CC130_WATER_METER_DEVICE (53)，系統將自動：
1. 建立 waterMeterNodeCc130 實例
2. 初始化 Modbus 通訊
3. 開始定期讀取水表數據
4. 更新系統資料庫

### 監控輸出
系統會輸出以下除錯訊息：
- `CC130 decimal_places: X` - 小數點位數讀取結果
- `CC130 water flow: X.XXX (raw: XXXX, decimal: X)` - 水表讀數計算結果
- `CC130 water meter update` - 系統更新訊息

## 注意事項

1. **數據依賴性：** 必須先成功讀取小數點位數，才能正確計算累計用量值
2. **通訊參數：** 確保 Modbus 通訊參數（波特率、停止位、校驗位）正確設定
3. **更新頻率：** 系統每分鐘更新一次水表讀數到資料庫
4. **數值範圍：** 支援 32-bit 無符號整數範圍的累計用量值
5. **PHP 系統相容性：** 所有水表相關的 PHP 查詢都已更新以支援 CC130 裝置類型
6. **資料庫整合：** CC130 水表的數據會自動儲存到 `#__water_meter_history` 表中

## 測試建議

1. **單元測試：** 測試 Modbus 命令建立和數據解析功能
2. **整合測試：** 驗證與實際 CC130 水表的通訊
3. **壓力測試：** 確認長時間運行的穩定性
4. **錯誤測試：** 驗證各種錯誤情況的處理

---

## 完成總結

### ✅ 已完成的修改

1. **C++ 核心實作**
   - 新增 `waterMeterNodeCc130.hpp` 和 `waterMeterNodeCc130.cpp`
   - 更新 `wdef.h` 定義裝置類型
   - 更新 `modbusNode.cpp` 註冊新裝置類型
   - 更新 `CMakeLists.txt` 建置設定

2. **PHP 系統整合**
   - 更新 `com_water_meter_history` 的 helper 檔案
   - 更新 `com_floor` 的 helper 檔案（2個方法）
   - 更新 `com_top` 的 helper 檔案（2個檔案，3個修改點）
   - 新增裝置類型中文描述

3. **文件記錄**
   - 建立詳細的修改記錄文件

### 📊 修改統計

- **新增檔案：** 2 個（.hpp + .cpp）
- **修改檔案：** 7 個
- **修改點數：** 12 個
- **涵蓋系統：** C++ 核心 + PHP Web 系統 + 建置系統

**完成狀態：** ✅ 所有修改已完成
**下一步：** 建議進行編譯測試和實際硬體測試
