#include <ctime>
#include <math.h>
#include "waterMeterNodeCc130.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10

/**
 * CC130 水表節點建構子
 * 初始化所有成員變數
 */
waterMeterNodeCc130::waterMeterNodeCc130()
{
  accumulateWaterFlow = 0;
  decimal_places = -1;
  update_timestamp = 0;
}

/**
 * CC130 水表節點解構子
 */
waterMeterNodeCc130::~waterMeterNodeCc130()
{
}

/**
 * 設定節點參數並建立 Modbus 訊息
 * 根據 CC130 規格建立讀取累計用量值和小數點位數的 Modbus 命令
 */
int waterMeterNodeCc130::set(Json::Value it)
{
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &m_dio_type, "dio_type");  
  set_double_value(it, &accumulateWaterFlow, "accumulateWaterFlow");
  
  // CC130 需要讀取的寄存器陣列
  int reg_arr[] =
      {
          10, // 小數點位數 (寄存器 10)
          0   // 累計用量值 (寄存器 0)
      };

  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  // 為每個寄存器建立 Modbus 讀取命令
  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;                    // 裝置地址
    send[1] = 0x03;                      // Function Code 3 (Read Holding Registers)
    send[2] = reg_arr[i] / 256;          // 寄存器地址高位元組
    send[3] = reg_arr[i] % 256;          // 寄存器地址低位元組
    
    // 設定讀取長度
    if (reg_arr[i] == 0) {
      // 累計用量值需要讀取 2 個寄存器 (4 bytes)
      send[4] = 0;
      send[5] = 0x02;
    } else {
      // 小數點位數只需要讀取 1 個寄存器 (2 bytes)
      send[4] = 0;
      send[5] = 0x01;
    }

    // 計算並添加 CRC 校驗碼
    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();
  return 1;
}

/**
 * 處理接收到的 Modbus 數據
 * 根據當前訊息索引處理不同類型的數據
 */
bool waterMeterNodeCc130::set_data(uint8_t *p_data, int len)
{
  // 檢查裝置地址是否匹配
  if (p_data[0] != m_addr)
  {
    return false;
  }

  switch (m_index)
  {
  case 0:
    // 處理小數點位數 (寄存器 10)
    // 根據 CC130 規格，只使用低位元組 (Y2)
    decimal_places = p_data[4];  // Y2 = Low byte
    cout << "CC130 decimal_places: " << decimal_places << endl;
    break;
    
  case 1:
    // 處理累計用量值 (寄存器 0)
    if (decimal_places > -1)
    {
      // 根據 CC130 規格計算累計用量值
      // 計算公式：X2 + (X1×256) + (X4×256²) + (X3×256³)
      uint8_t X1 = p_data[3];  // Data1 High byte
      uint8_t X2 = p_data[4];  // Data1 Low byte
      uint8_t X3 = p_data[5];  // Data2 High byte
      uint8_t X4 = p_data[6];  // Data2 Low byte
      
      uint32_t raw_value = X2 + (X1 * 256) + (X4 * 256 * 256) + (X3 * 256 * 256 * 256);
      
      // 根據小數點位數計算最終數值
      accumulateWaterFlow = raw_value / pow(10, decimal_places);
      
      cout << "CC130 water flow: " << accumulateWaterFlow << " (raw: " << raw_value << ", decimal: " << decimal_places << ")" << endl;
      
      // 更新系統訊息
      updateMessage();
      decimal_places = -1;  // 重置小數點位數，等待下次讀取
    }
    else
    {
      cout << "CC130 water flow reading failed - decimal_places not ready: " << decimal_places << endl;
    }
    break;
  }

  m_index++;

  // 重置索引以循環讀取
  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}

/**
 * 取得節點索引
 */
int waterMeterNodeCc130::getIndex()
{
  return index;
}

/**
 * 取得程序 ID
 */
int waterMeterNodeCc130::getPid()
{
  return m_pid;
}

/**
 * 更新訊息到系統
 * 定期將水表讀數更新到資料庫
 */
void waterMeterNodeCc130::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  
  // 每分鐘更新一次
  if (current_timestamp > (update_timestamp + 1 * 60))
  {
    update_timestamp = current_timestamp;
    static int s_cnt = MAX_UPDATEMESSAGE;
    bool is_show = false;
    
    if (++s_cnt > MAX_UPDATEMESSAGE)
    {
      s_cnt = 0;
      is_show = true;
    }
    
    // 建立更新訊息
    stringstream ss;
    ss << "/index.php?option=\"com_floor&task=sroots.update_watermeter&accumulateWaterFlow=" 
       << accumulateWaterFlow << "&id=" << m_id << "\" ";
    
    WSendMsg msg;
    cout << "CC130 water meter update" << endl;
    msg.set(ss.str(), "/tmp/sendrs485watermetermsg", "/tmp/SENDRS485WATERMETERMSG", true, false);
    baUtil::add_sendMessage(&msg);
  }
}
